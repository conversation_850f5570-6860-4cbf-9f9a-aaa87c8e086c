import { motion } from "framer-motion";
import { Link } from "react-router-dom";

const Home = () => {
  // Helper function to format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Set default file size limit for guests (100MB)
  const maxFileSize = 100 * 1024 * 1024; // 100MB in bytes
  const userType = "Guest";

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="page"
    >
      {/* Hero Section */}
      <section className="hero">
        <div className="container-center">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="hero-content"
          >
            <h1 className="hero-title">
              Share Files Like
              <span className="gradient-text"> Never Before</span>
            </h1>
            <p className="hero-subtitle">
              Upload, share, and manage your files with revolutionary features
              like QR codes, selective downloads, bulk uploads, and cross-device
              compatibility.
            </p>

            <div className="hero-features">
              <div className="feature-item">
                <span className="feature-icon">🚀</span>
                <span>Lightning Fast</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🔒</span>
                <span>Secure & Private</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">📱</span>
                <span>Cross-Device</span>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🌍</span>
                <span>Global Access</span>
              </div>
            </div>

            <div className="hero-stats">
              <div className="stat-item">
                <span className="stat-number">∞</span>
                <span className="stat-label">Files Shared</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">
                  {formatFileSize(maxFileSize)}
                </span>
                <span className="stat-label">Max File Size ({userType})</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">20</span>
                <span className="stat-label">Bulk Upload Limit</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Upload Options Section */}
      <section className="upload-section">
        <div className="container-center">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="upload-container"
          >
            <div className="upload-header">
              <h2>Choose Your Upload Method</h2>
              <p>
                Select the perfect upload option for your needs. Get instant
                shareable links with QR codes!
              </p>
            </div>

            <div className="upload-options-grid">
              <motion.div
                initial={{ x: -30, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="upload-option-card"
              >
                <div className="option-icon">📄</div>
                <h3>Single File Upload</h3>
                <p>
                  Upload one file at a time with smart compression and instant
                  preview links
                </p>
                <ul className="option-features">
                  <li>✅ Smart image compression</li>
                  <li>✅ Instant QR code generation</li>
                  <li>✅ Preview page with download stats</li>
                  <li>✅ Cross-device sharing</li>
                </ul>
                <Link to="/upload" className="btn btn-primary btn-large">
                  📄 Upload Single File
                </Link>
              </motion.div>

              <motion.div
                initial={{ x: 30, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 1.0 }}
                className="upload-option-card"
              >
                <div className="option-icon">📦</div>
                <h3>Bulk File Upload</h3>
                <p>
                  Upload up to 20 files at once with selective download options
                </p>
                <ul className="option-features">
                  <li>✅ Upload up to 20 files</li>
                  <li>✅ Auto-generated ZIP files</li>
                  <li>✅ Selective download with checkboxes</li>
                  <li>✅ Batch management tools</li>
                </ul>
                <Link to="/bulk-upload" className="btn btn-secondary btn-large">
                  📦 Upload Multiple Files
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <div className="container-center">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="features-grid"
          >
            <h2 className="section-title">Revolutionary Features</h2>
            <p
              style={{
                fontSize: "1.25rem",
                color: "var(--text-secondary)",
                maxWidth: "48rem",
                margin: "0 auto",
              }}
            >
              Discover what makes QuickShare the most advanced file-sharing
              platform
            </p>
          </motion.div>

          <div className="features-list">
            {[
              {
                icon: "🎯",
                title: "Selective Download",
                desc: "Choose exactly which files to download with checkboxes",
              },
              {
                icon: "📱",
                title: "QR Code Sharing",
                desc: "Every file gets a QR code for instant mobile sharing",
              },
              {
                icon: "📦",
                title: "Bulk Upload & ZIP",
                desc: "Upload 20 files at once with auto-generated ZIP",
              },
              {
                icon: "🖼️",
                title: "Smart Thumbnails",
                desc: "Automatic thumbnail generation for images",
              },
              {
                icon: "⏰",
                title: "Auto-Expiration",
                desc: "Set custom expiration dates for security",
              },
              {
                icon: "🔐",
                title: "Password Protection",
                desc: "Add password protection to sensitive files",
              },
              {
                icon: "📊",
                title: "Download Analytics",
                desc: "Track downloads and access patterns",
              },
              {
                icon: "🌐",
                title: "Cross-Device Access",
                desc: "Access files from any device, anywhere",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9 + index * 0.1 }}
                className="feature-card"
              >
                <span className="feature-icon">{feature.icon}</span>
                <h3>{feature.title}</h3>
                <p>{feature.desc}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container-center">
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 1.2 }}
            className="cta-content"
          >
            <h2>Ready to Share More Files?</h2>
            <p>Experience the power of bulk uploads and selective downloads.</p>
            <div className="cta-buttons">
              <Link
                to="/bulk-upload"
                className="btn btn-primary"
                style={{
                  background: "white",
                  color: "#667eea",
                  fontWeight: "600",
                }}
              >
                📦 Try Bulk Upload
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </motion.div>
  );
};

export default Home;
