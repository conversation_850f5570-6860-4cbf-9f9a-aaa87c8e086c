import { useState } from "react";
import { motion } from "framer-motion";
import { useDropzone } from "react-dropzone";
import axios from "axios";
import toast from "react-hot-toast";
import { API_ENDPOINTS, uploadAxiosConfig } from "../config/api";
import { compressImage, shouldCompress, getCompressionStats, formatFileSize } from "../utils/fileCompression";

const SingleUpload = () => {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isCompressing, setIsCompressing] = useState(false);
  const [compressionStats, setCompressionStats] = useState(null);
  const [uploadResult, setUploadResult] = useState(null);

  const onDrop = (acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      const selectedFile = acceptedFiles[0];
      setFile({
        file: selectedFile,
        preview: selectedFile.type.startsWith('image/') ? URL.createObjectURL(selectedFile) : null,
        progress: 0,
        status: "ready"
      });
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxFiles: 1,
    maxSize: 100 * 1024 * 1024, // 100MB
  });

  const removeFile = () => {
    if (file?.preview) {
      URL.revokeObjectURL(file.preview);
    }
    setFile(null);
    setUploadResult(null);
    setCompressionStats(null);
  };

  const uploadFile = async () => {
    if (!file) return;

    try {
      setUploading(true);
      setUploadProgress(0);

      let fileToUpload = file.file;
      
      // Compress image if applicable
      if (shouldCompress(file.file)) {
        setIsCompressing(true);
        const originalFile = file.file;
        fileToUpload = await compressImage(file.file);
        
        const stats = getCompressionStats(originalFile, fileToUpload);
        setCompressionStats(stats);
        setIsCompressing(false);
      }

      const formData = new FormData();
      formData.append("file", fileToUpload);

      const response = await axios.post(
        API_ENDPOINTS.UPLOAD_SINGLE,
        formData,
        {
          ...uploadAxiosConfig,
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            setUploadProgress(percentCompleted);
            setFile(prev => ({
              ...prev,
              progress: percentCompleted,
              status: percentCompleted === 100 ? "completed" : "uploading"
            }));
          },
        }
      );

      if (response.data.success) {
        setUploadResult(response.data.file);
        setFile(prev => ({ ...prev, status: "completed" }));
        toast.success("File uploaded successfully!");
      } else {
        throw new Error(response.data.message || "Upload failed");
      }
    } catch (error) {
      console.error("Upload error:", error);
      console.error("Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: error.config
      });

      let errorMessage = "Upload failed. Please try again.";
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 413) {
        errorMessage = "File too large. Please choose a smaller file.";
      } else if (error.response?.status === 429) {
        errorMessage = "Too many upload attempts. Please wait and try again.";
      } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
      setFile(prev => ({ ...prev, status: "error" }));
    } finally {
      setUploading(false);
      setUploadProgress(0);
      setIsCompressing(false);
    }
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Link copied to clipboard!");
    } catch (err) {
      console.error("Failed to copy: ", err);
      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand("copy");
        toast.success("Link copied to clipboard!");
      } catch (err) {
        console.error("Fallback copy failed: ", err);
        toast.error("Failed to copy link");
      }
      document.body.removeChild(textArea);
    }
  };

  const getFileIcon = (mimetype) => {
    if (mimetype.startsWith("image/")) return "🖼️";
    if (mimetype.startsWith("video/")) return "🎥";
    if (mimetype.startsWith("audio/")) return "🎵";
    if (mimetype.includes("pdf")) return "📄";
    if (mimetype.includes("word") || mimetype.includes("document")) return "📝";
    if (mimetype.includes("excel") || mimetype.includes("spreadsheet")) return "📊";
    if (mimetype.includes("powerpoint") || mimetype.includes("presentation")) return "📈";
    if (mimetype.includes("zip") || mimetype.includes("rar") || mimetype.includes("archive")) return "📦";
    return "📁";
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="page bulk-upload-page"
    >
      <div className="container">
        <div className="bulk-header">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <h1>📄 Single File Upload</h1>
            <p>Upload one file with smart compression and instant sharing</p>
          </motion.div>
        </div>

        {!uploadResult && (
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="upload-zone-container"
          >
            <div
              {...getRootProps()}
              className={`upload-zone ${isDragActive ? "drag-active" : ""} ${
                file ? "has-files" : ""
              }`}
            >
              <input {...getInputProps()} />
              {!file ? (
                <div className="upload-zone-content">
                  <div className="upload-icon">📄</div>
                  <h3>Drop your file here or click to browse</h3>
                  <p>Maximum file size: 100MB</p>
                  <div className="supported-formats">
                    <span>Supports: Images, Videos, Documents, Archives, and more</span>
                  </div>
                </div>
              ) : (
                <div className="file-preview">
                  <div className="file-item">
                    <div className="file-icon">{getFileIcon(file.file.type)}</div>
                    <div className="file-info">
                      <h4>{file.file.name}</h4>
                      <p>{formatFileSize(file.file.size)}</p>
                      <span className="file-type">{file.file.type}</span>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile();
                      }}
                      className="remove-file"
                    >
                      ✕
                    </button>
                  </div>
                  
                  {file.preview && (
                    <div className="image-preview">
                      <img src={file.preview} alt="Preview" />
                    </div>
                  )}
                </div>
              )}
            </div>

            {isCompressing && (
              <div className="compression-status">
                <div className="spinner"></div>
                <p>🗜️ Compressing image to save bandwidth...</p>
              </div>
            )}

            {compressionStats && compressionStats.wasCompressed && (
              <div className="compression-stats">
                <p>✅ Image compressed: {formatFileSize(compressionStats.originalSize)} → {formatFileSize(compressionStats.compressedSize)} ({compressionStats.savingsPercent}% smaller)</p>
              </div>
            )}

            {file && !uploading && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                className="upload-controls"
              >
                <button
                  onClick={uploadFile}
                  className="btn btn-primary btn-large"
                >
                  🚀 Upload File
                </button>
                <button onClick={removeFile} className="btn btn-outline">
                  🗑️ Remove File
                </button>
              </motion.div>
            )}

            {uploading && (
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                className="upload-progress"
              >
                <div className="progress-header">
                  <h3>Uploading File...</h3>
                  <span>{uploadProgress}%</span>
                </div>
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </motion.div>
            )}
          </motion.div>
        )}

        {uploadResult && (
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className="upload-success"
          >
            <div className="success-header">
              <div className="success-icon">✅</div>
              <h2>File Uploaded Successfully!</h2>
              <p>Your file is ready to share</p>
            </div>

            <div className="file-result">
              <div className="file-icon">{getFileIcon(uploadResult.mimetype)}</div>
              <div className="file-details">
                <h3>{uploadResult.originalName}</h3>
                <p>{formatFileSize(uploadResult.size)}</p>
                <span className="file-type">{uploadResult.mimetype}</span>
              </div>
            </div>

            <div className="share-links">
              <div className="link-item">
                <label>Preview & Download Link:</label>
                <div className="link-container">
                  <input
                    type="text"
                    value={uploadResult.previewLink || uploadResult.downloadLink}
                    readOnly
                    className="link-input"
                  />
                  <button
                    onClick={() => copyToClipboard(uploadResult.previewLink || uploadResult.downloadLink)}
                    className="btn btn-outline"
                  >
                    📋 Copy
                  </button>
                </div>
              </div>

              {uploadResult.qrCode && (
                <div className="qr-code-section">
                  <h4>📱 QR Code for Mobile Sharing</h4>
                  <img src={uploadResult.qrCode} alt="QR Code" className="qr-code" />
                </div>
              )}
            </div>

            <div className="action-buttons">
              <button
                onClick={() => {
                  setUploadResult(null);
                  setFile(null);
                  setCompressionStats(null);
                }}
                className="btn btn-primary"
              >
                📄 Upload Another File
              </button>
              <a
                href={uploadResult.previewLink || uploadResult.downloadLink}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-outline"
              >
                👁️ View File Page
              </a>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default SingleUpload;
