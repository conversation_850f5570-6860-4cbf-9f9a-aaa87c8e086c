const multer = require("multer");
const path = require("path");
const fs = require("fs");
const { v4: uuidv4 } = require("uuid");

// Ensure upload directories exist
const createUploadDirs = () => {
  const dirs = [
    "uploads",
    "uploads/files",
    "uploads/thumbnails",
    "uploads/temp",
  ];

  dirs.forEach((dir) => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
};

// Create directories on startup
createUploadDirs();

// Configure storage for uploaded files
const storage = multer.diskStorage({
  // Set destination folder for uploaded files
  destination: function (req, file, cb) {
    cb(null, "uploads/files/"); // Files will be stored in uploads/files/ directory
  },

  // Generate unique filename to avoid conflicts
  filename: function (req, file, cb) {
    // Generate unique filename: timestamp + uuid + original extension
    const uniqueSuffix = Date.now() + "-" + uuidv4();
    const fileExtension = path.extname(file.originalname);
    cb(null, uniqueSuffix + fileExtension);
  },
});

// Enhanced file filter with security checks
const fileFilter = (req, file, cb) => {
  // Blocked file extensions for security
  const blockedExtensions = [
    ".exe",
    ".bat",
    ".cmd",
    ".com",
    ".pif",
    ".scr",
    ".vbs",
    ".js",
    ".jar",
    ".app",
    ".deb",
    ".pkg",
    ".dmg",
    ".rpm",
    ".msi",
    ".run",
  ];

  const fileExtension = path.extname(file.originalname).toLowerCase();

  // Check for blocked extensions
  if (blockedExtensions.includes(fileExtension)) {
    return cb(
      new Error(
        `File type ${fileExtension} is not allowed for security reasons`
      ),
      false
    );
  }

  // Check for suspicious filenames
  if (
    file.originalname.includes("..") ||
    file.originalname.includes("/") ||
    file.originalname.includes("\\")
  ) {
    return cb(new Error("Invalid filename"), false);
  }

  cb(null, true); // Accept file
};

// Get file size limit based on user type - INCREASED FOR UNIQUENESS!
const getFileSizeLimit = (req) => {
  // Premium users get massive limits - industry leading!
  if (req.user && req.user.isPremium) {
    return 10 * 1024 * 1024 * 1024; // 10GB for premium users - HUGE!
  }

  // Registered users get generous limits
  if (req.user) {
    return 2 * 1024 * 1024 * 1024; // 2GB for registered users - GENEROUS!
  }

  // Guest users get industry-leading limits - 10x more than competitors!
  return 1 * 1024 * 1024 * 1024; // 1GB for guest users - UNIQUE!
};

// Configure multer for single file upload
const uploadSingle = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 * 1024, // 10GB max (will be checked dynamically)
    files: 1,
  },
});

// Configure multer for multiple file upload (bulk upload)
const uploadMultiple = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 * 1024, // 10GB per file max
    files: 20, // Maximum 20 files at once
  },
});

// Middleware to check file size limits dynamically
const checkFileSize = (req, res, next) => {
  const maxSize = getFileSizeLimit(req);

  if (req.file && req.file.size > maxSize) {
    return res.status(413).json({
      success: false,
      message: `File too large. Maximum size allowed: ${formatFileSize(
        maxSize
      )}`,
      maxSize: maxSize,
      userType: req.user
        ? req.user.isPremium
          ? "premium"
          : "registered"
        : "guest",
    });
  }

  if (req.files && req.files.length > 0) {
    const oversizedFiles = req.files.filter((file) => file.size > maxSize);
    if (oversizedFiles.length > 0) {
      return res.status(413).json({
        success: false,
        message: `Some files are too large. Maximum size allowed: ${formatFileSize(
          maxSize
        )}`,
        maxSize: maxSize,
        oversizedFiles: oversizedFiles.map((f) => f.originalname),
        userType: req.user
          ? req.user.isPremium
            ? "premium"
            : "registered"
          : "guest",
      });
    }
  }

  next();
};

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

module.exports = {
  uploadSingle,
  uploadMultiple,
  checkFileSize,
  formatFileSize,
  createUploadDirs,
};
