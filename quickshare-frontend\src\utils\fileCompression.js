// File compression utilities for QuickShare
// This is a unique feature that compresses images before upload to save bandwidth and storage

/**
 * Compress an image file before upload
 * @param {File} file - The image file to compress
 * @param {number} quality - Compression quality (0.1 to 1.0)
 * @param {number} maxWidth - Maximum width in pixels
 * @param {number} maxHeight - Maximum height in pixels
 * @returns {Promise<File>} - Compressed file
 */
export const compressImage = (file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) => {
  return new Promise((resolve, reject) => {
    // Only compress images
    if (!file.type.startsWith('image/')) {
      resolve(file);
      return;
    }

    // Skip compression for very small files (< 100KB)
    if (file.size < 100 * 1024) {
      resolve(file);
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            // Create new file with compressed data
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            resolve(file); // Fallback to original
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => resolve(file); // Fallback to original
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Get compression savings information
 * @param {File} originalFile 
 * @param {File} compressedFile 
 * @returns {Object} Compression stats
 */
export const getCompressionStats = (originalFile, compressedFile) => {
  const originalSize = originalFile.size;
  const compressedSize = compressedFile.size;
  const savings = originalSize - compressedSize;
  const savingsPercent = Math.round((savings / originalSize) * 100);

  return {
    originalSize,
    compressedSize,
    savings,
    savingsPercent,
    wasCompressed: compressedSize < originalSize,
  };
};

/**
 * Format file size for display
 * @param {number} bytes 
 * @returns {string} Formatted size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Check if file should be compressed
 * @param {File} file 
 * @returns {boolean}
 */
export const shouldCompress = (file) => {
  // Only compress images
  if (!file.type.startsWith('image/')) return false;
  
  // Skip small files
  if (file.size < 100 * 1024) return false;
  
  // Skip already compressed formats
  const skipFormats = ['image/webp', 'image/avif'];
  if (skipFormats.includes(file.type)) return false;
  
  return true;
};

/**
 * Batch compress multiple files
 * @param {File[]} files 
 * @param {Function} onProgress 
 * @returns {Promise<File[]>}
 */
export const batchCompressFiles = async (files, onProgress = () => {}) => {
  const compressedFiles = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    if (shouldCompress(file)) {
      const compressed = await compressImage(file);
      compressedFiles.push(compressed);
    } else {
      compressedFiles.push(file);
    }
    
    onProgress(i + 1, files.length);
  }
  
  return compressedFiles;
};
