import { useState, useRef } from "react";
import axios from "axios";
import { API_ENDPOINTS, uploadAxiosConfig } from "../config/api";
import {
  compressImage,
  shouldCompress,
  getCompressionStats,
  formatFileSize,
} from "../utils/fileCompression";

const FileUpload = ({ onUploadStart, onUploadSuccess, onUploadError }) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isCompressing, setIsCompressing] = useState(false);
  const [compressionStats, setCompressionStats] = useState(null);
  const fileInputRef = useRef(null);

  // Handle file selection
  const handleFileSelect = (files) => {
    if (files && files.length > 0) {
      uploadFile(files[0]);
    }
  };

  // Upload file to backend
  const uploadFile = async (file) => {
    try {
      // Notify parent component that upload started
      onUploadStart();

      let fileToUpload = file;

      // Compress image if applicable
      if (shouldCompress(file)) {
        setIsCompressing(true);
        const originalFile = file;
        fileToUpload = await compressImage(file);

        const stats = getCompressionStats(originalFile, fileToUpload);
        setCompressionStats(stats);
        setIsCompressing(false);
      }

      // Create FormData object
      const formData = new FormData();
      formData.append("file", fileToUpload);

      // Upload file with progress tracking
      const response = await axios.post(API_ENDPOINTS.UPLOAD_SINGLE, formData, {
        ...uploadAxiosConfig,
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress(percentCompleted);
        },
      });

      // Handle successful upload
      if (response.data.success) {
        onUploadSuccess(response.data.file);
      } else {
        throw new Error(response.data.message || "Upload failed");
      }
    } catch (error) {
      console.error("Upload error:", error);
      console.error("Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: error.config,
      });

      onUploadError();

      // Show user-friendly error message
      let errorMessage = "Upload failed. Please try again.";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 413) {
        errorMessage = "File too large. Please choose a smaller file.";
      } else if (error.response?.status === 429) {
        errorMessage = "Too many upload attempts. Please wait and try again.";
      } else if (
        error.code === "NETWORK_ERROR" ||
        error.message.includes("Network Error")
      ) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    } finally {
      setUploadProgress(0);
      setIsCompressing(false);
      setCompressionStats(null);
    }
  };

  // Handle drag events
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  // Handle drop event
  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  // Handle file input change
  const handleInputChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files);
    }
  };

  // Open file dialog
  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className="file-upload">
      <div
        className={`upload-area ${dragActive ? "drag-active" : ""}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleInputChange}
          style={{ display: "none" }}
        />

        <div className="upload-content">
          <div className="upload-icon">📁</div>
          <h3>Drop your file here or click to browse</h3>
          <p>Maximum file size: 50MB</p>
          <p>All file types supported</p>
        </div>
      </div>

      {uploadProgress > 0 && (
        <div className="progress-container">
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
          <p>{uploadProgress}% uploaded</p>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
