/* QuickShare - Beautiful Professional Design with Tailwind Integration */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

#root {
  min-height: 100vh;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  transition: background-color 0.3s ease;
}

/* Page Layout */
.page {
  flex: 1;
  padding-top: 80px;
}

/* Beautiful Container */
.container-center {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Navbar Styles - FIXED MISSING CLASSES */
.navbar {
  background: var(--navbar-bg);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-logo {
  text-decoration: none;
  color: var(--text-primary);
}

.logo-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  font-size: 1.5rem;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--text-primary);
}

.navbar-menu {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.navbar-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  position: relative;
}

.navbar-link:hover {
  color: var(--accent-primary);
  background: rgba(102, 126, 234, 0.1);
}

.navbar-link.active {
  color: var(--accent-primary);
  background: rgba(102, 126, 234, 0.1);
}

.navbar-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.theme-toggle {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  background: var(--bg-tertiary);
}

.user-menu {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem 1rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  color: var(--text-primary);
}

.user-button:hover {
  background: var(--bg-tertiary);
}

.user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: var(--accent-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.user-name {
  font-weight: 600;
  font-size: 0.875rem;
}

.premium-badge {
  font-size: 0.75rem;
  color: var(--accent-primary);
}

.dropdown-arrow {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  transition: transform 0.3s ease;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  min-width: 280px;
  z-index: 1000;
  margin-top: 0.5rem;
  overflow: hidden;
}

.dropdown-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.user-details strong {
  display: block;
  color: var(--text-primary);
  font-weight: 600;
}

.user-details span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.storage-info {
  margin-top: 0.75rem;
}

.storage-bar {
  width: 100%;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.storage-fill {
  height: 100%;
  background: var(--accent-primary);
  transition: width 0.3s ease;
}

.storage-text {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.dropdown-menu {
  padding: 0.5rem;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--text-primary);
  background: none;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 0.875rem;
}

.dropdown-item:hover {
  background: var(--bg-tertiary);
}

.dropdown-item.premium-upgrade {
  color: var(--accent-primary);
  font-weight: 600;
}

.dropdown-item.logout {
  color: var(--error);
}

.dropdown-divider {
  border: none;
  border-top: 1px solid var(--border-color);
  margin: 0.5rem 0;
}

.auth-buttons {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 3px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu-toggle span {
  width: 20px;
  height: 2px;
  background: var(--text-primary);
  transition: all 0.3s ease;
}

.mobile-menu {
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
  padding: 1rem;
}

.mobile-link {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.mobile-link:hover {
  background: var(--bg-tertiary);
}

.mobile-link.logout {
  color: var(--error);
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

/* Hero Section */
.hero {
  background: var(--hero-bg);
  color: white;
  position: relative;
  overflow: hidden;
  padding: 5rem 0 4rem;
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(120, 119, 198, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 119, 198, 0.3) 0%,
      transparent 50%
    );
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 1000px;
  margin: 0 auto;
}

.hero-title {
  font-size: clamp(2.5rem, 8vw, 5rem);
  font-weight: 900;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
  font-size: clamp(1.125rem, 3vw, 1.5rem);
  margin-bottom: 2rem;
  opacity: 0.95;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.gradient-text {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
  animation: shimmer 3s ease-in-out infinite;
}

/* Feature Badges */
.hero-features {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 9999px;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.18);
  transition: all 0.3s ease;
  animation: float 6s ease-in-out infinite;
}

.feature-item:nth-child(2) {
  animation-delay: -2s;
}

.feature-item:nth-child(3) {
  animation-delay: -4s;
}

.feature-item:nth-child(4) {
  animation-delay: -1s;
}

.feature-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.35);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

/* Statistics */
.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: scale(1.05);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 0.5rem;
  display: block;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Professional Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  text-decoration: none;
  transform: translateY(0);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.15);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.btn-secondary:hover {
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.btn-outline:hover {
  border-color: var(--accent-primary);
  color: var(--accent-primary);
  background: rgba(102, 126, 234, 0.05);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

/* Beautiful Cards */
.card {
  background: var(--card-bg);
  border-radius: 1.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::before {
  opacity: 1;
}

.card:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateY(-4px);
  border-color: var(--accent-primary);
}

/* Upload Section */
.upload-section {
  padding: 5rem 0;
  background: var(--bg-secondary);
}

.upload-container {
  max-width: 1000px;
  margin: 0 auto;
}

.upload-header {
  text-align: center;
  margin-bottom: 3rem;
}

.upload-header h2 {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.upload-header p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0;
}

.upload-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.upload-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.upload-option-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.upload-option-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border-color: var(--accent-primary);
}

.option-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.upload-option-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 600;
}

.upload-option-card p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.option-features {
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
  text-align: left;
}

.option-features li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 1rem;
  width: 100%;
}

.upload-status {
  text-align: center;
  padding: 3rem;
}

.upload-status h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.upload-status p {
  color: var(--text-secondary);
  margin: 0;
}

/* Loading Spinners */
.spinner {
  width: 2rem;
  height: 2rem;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-large {
  width: 3rem;
  height: 3rem;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Keyframe Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translateY(0);
  }
  40%,
  43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
  90% {
    transform: translateY(-2px);
  }
}

/* Features Section */
.features-section {
  padding: 5rem 0;
  background: var(--bg-secondary);
  position: relative;
}

.features-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 20%,
      rgba(102, 126, 234, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(118, 75, 162, 0.05) 0%,
      transparent 50%
    );
  z-index: 1;
}

.features-grid {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
  z-index: 2;
}

.section-title {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  position: relative;
  z-index: 2;
}

.feature-card {
  background: var(--card-bg);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feature-card:hover::before {
  opacity: 0.05;
}

.feature-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border-color: var(--accent-primary);
}

.feature-card .feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
  position: relative;
  z-index: 2;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
  position: relative;
  z-index: 2;
}

/* CTA Section */
.cta-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%
    );
  z-index: 1;
}

.cta-content {
  position: relative;
  z-index: 2;
}

.cta-content h2 {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cta-content p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.95;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cta-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(10px);
}

.loading-content {
  text-align: center;
  animation: fadeInUp 0.6s ease-out;
}

.loading-content h2 {
  margin: 1rem 0 0.5rem;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-content p {
  color: var(--text-secondary);
  margin: 0;
}

/* Footer */
.app-footer {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  color: white;
  padding: 3rem 0 1rem;
  margin-top: auto;
  position: relative;
  overflow: hidden;
}

.app-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(102, 126, 234, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(118, 75, 162, 0.1) 0%,
      transparent 50%
    );
  z-index: 1;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  position: relative;
  z-index: 2;
}

.footer-section h3 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, #60a5fa, #a78bfa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-section h4 {
  margin-bottom: 0.5rem;
  color: #cbd5e1;
  font-weight: 600;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin-bottom: 0.25rem;
  color: #94a3b8;
  transition: color 0.3s ease;
}

.footer-section li:hover {
  color: #cbd5e1;
}

.footer-bottom {
  text-align: center;
  padding: 1rem;
  border-top: 1px solid #374151;
  margin-top: 2rem;
  color: #94a3b8;
  position: relative;
  z-index: 2;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .navbar-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .features-list {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 3rem 0 2rem;
  }

  .hero-features {
    gap: 1rem;
    flex-direction: column;
    align-items: center;
  }

  .feature-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .upload-section {
    padding: 3rem 0;
  }

  .upload-options {
    flex-direction: column;
    align-items: center;
  }

  .upload-options-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .upload-option-card {
    padding: 1.5rem;
  }

  .features-section {
    padding: 3rem 0;
  }

  .features-list {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .cta-section {
    padding: 3rem 0;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .navbar-container {
    padding: 0 1rem;
  }

  .navbar-actions {
    gap: 0.5rem;
  }

  .user-dropdown {
    min-width: 250px;
    right: -1rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 1.5rem;
  }

  .app-footer {
    padding: 2rem 0 1rem;
  }
}

@media (max-width: 480px) {
  .container-center {
    padding: 0 0.75rem;
  }

  .hero {
    padding: 2rem 0 1.5rem;
  }

  .hero-title {
    font-size: clamp(1.75rem, 8vw, 2.5rem);
  }

  .hero-subtitle {
    font-size: clamp(1rem, 4vw, 1.25rem);
  }

  .upload-section,
  .features-section,
  .cta-section {
    padding: 2rem 0;
  }

  .card {
    margin: 0 0.5rem;
  }

  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.8125rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .navbar-container {
    padding: 0 0.75rem;
  }

  .logo-text {
    font-size: 1.25rem;
  }

  .user-dropdown {
    min-width: 220px;
    right: -0.5rem;
  }
}

/* Dark mode specific adjustments */
@media (prefers-color-scheme: dark) {
  [data-theme="auto"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --border-color: #334155;
    --card-bg: #1e293b;
    --navbar-bg: rgba(15, 23, 42, 0.95);
    --hero-bg: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    --accent-primary: #60a5fa;
    --accent-secondary: #a78bfa;
  }
}

/* Auth Pages Styles */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  padding: 2rem 1rem;
}

.auth-container {
  width: 100%;
  max-width: 500px;
}

.auth-card {
  background: var(--card-bg);
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.auth-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%
  );
  z-index: 1;
}

.auth-card > * {
  position: relative;
  z-index: 2;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-header p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

.auth-form {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 0.75rem;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
  outline: none;
}

.form-input:focus {
  border-color: var(--accent-primary);
  background: var(--card-bg);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: var(--error);
  background: rgba(239, 68, 68, 0.05);
}

.form-error {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.form-error::before {
  content: "⚠️";
  font-size: 0.75rem;
}

.btn-full {
  width: 100%;
  justify-content: center;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
}

.auth-footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
  margin-bottom: 2rem;
}

.auth-footer p {
  color: var(--text-secondary);
  margin: 0;
}

.auth-link {
  color: var(--accent-primary);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: var(--accent-secondary);
  text-decoration: underline;
}

.auth-features {
  background: var(--bg-secondary);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.auth-features h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
}

.auth-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.auth-features li {
  padding: 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-benefits {
  background: var(--bg-secondary);
  border-radius: 1rem;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.auth-benefits h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
}

.benefits-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--card-bg);
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  font-size: 1.25rem;
}

.benefit-item span:last-child {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

/* Dashboard Page Styles */
.dashboard-page {
  padding: 2rem 0;
  min-height: 100vh;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 3rem;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-primary);
}

.stat-icon {
  font-size: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--bg-secondary);
  border-radius: 1rem;
  border: 1px solid var(--border-color);
  position: relative;
  z-index: 2;
}

.stat-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.stat-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 500;
}

.storage-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.storage-card h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.storage-visual {
  margin-bottom: 1.5rem;
}

.storage-bar-large {
  width: 100%;
  height: 12px;
  background: var(--bg-tertiary);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.storage-fill-large {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  border-radius: 6px;
  transition: width 0.3s ease;
  position: relative;
}

.storage-fill-large::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

.storage-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.storage-info-text {
  text-align: center;
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.quick-actions {
  margin-bottom: 3rem;
}

.quick-actions h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
  text-align: center;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.action-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 2rem;
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.action-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-card:hover::before {
  opacity: 1;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-primary);
}

.action-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-card.disabled:hover {
  transform: none;
  box-shadow: none;
  border-color: var(--border-color);
}

.action-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  position: relative;
  z-index: 2;
}

.action-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  position: relative;
  z-index: 2;
}

.action-card p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
  position: relative;
  z-index: 2;
}

.recent-files {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.recent-files h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
  text-align: center;
}

.files-placeholder {
  text-align: center;
  padding: 3rem 2rem;
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.files-placeholder h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.files-placeholder p {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.loading-state {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-state p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-top: 1.5rem;
}

/* Dashboard Responsive Styles */
@media (max-width: 768px) {
  .dashboard-page {
    padding: 1rem 0;
  }

  .dashboard-header h1 {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    padding: 1.5rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }

  .stat-content h3 {
    font-size: 1.5rem;
  }

  .storage-card {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-card {
    padding: 1.5rem;
  }

  .recent-files {
    padding: 1.5rem;
  }

  .files-placeholder {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header h1 {
    font-size: 1.75rem;
  }

  .dashboard-header p {
    font-size: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .stat-content h3 {
    font-size: 1.25rem;
  }

  .storage-card {
    padding: 1rem;
  }

  .storage-card h2 {
    font-size: 1.25rem;
  }

  .action-card {
    padding: 1rem;
  }

  .action-icon {
    font-size: 2rem;
  }

  .action-card h3 {
    font-size: 1.125rem;
  }
}

/* Download Page Styles */
.download-page {
  padding: 2rem 0;
  min-height: 100vh;
}

.download-container {
  max-width: 800px;
  margin: 0 auto;
}

.download-header {
  text-align: center;
  margin-bottom: 3rem;
}

.download-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.download-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
}

.file-preview-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.file-preview-card.large {
  padding: 3rem;
}

.file-preview-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.file-icon {
  font-size: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  background: var(--bg-secondary);
  border-radius: 1rem;
  border: 1px solid var(--border-color);
}

.file-icon.large {
  font-size: 5rem;
  width: 120px;
  height: 120px;
}

.file-details h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  word-break: break-word;
}

.file-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.file-meta span {
  background: var(--bg-tertiary);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
}

.download-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.error-container {
  text-align: center;
  padding: 4rem 2rem;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.error-container h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.error-container p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.loading-container {
  text-align: center;
  padding: 4rem 2rem;
}

.loading-container h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.loading-container p {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

.spinner-large {
  width: 60px;
  height: 60px;
  border: 4px solid var(--bg-tertiary);
  border-top: 4px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 2rem;
}

/* Download Page Responsive Styles */
@media (max-width: 768px) {
  .download-page {
    padding: 1rem 0;
  }

  .download-header h1 {
    font-size: 2rem;
  }

  .file-preview-card.large {
    padding: 2rem;
  }

  .file-preview-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .file-icon.large {
    font-size: 4rem;
    width: 100px;
    height: 100px;
  }

  .file-details h2 {
    font-size: 1.5rem;
  }

  .file-meta {
    justify-content: center;
  }

  .download-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-large {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .download-header h1 {
    font-size: 1.75rem;
  }

  .file-preview-card.large {
    padding: 1.5rem;
  }

  .file-icon.large {
    font-size: 3rem;
    width: 80px;
    height: 80px;
  }

  .file-details h2 {
    font-size: 1.25rem;
  }

  .file-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .file-meta span {
    text-align: center;
  }
}

/* Print styles */
@media print {
  .navbar,
  .app-footer,
  .cta-section {
    display: none;
  }

  .page {
    padding-top: 0;
  }

  .hero {
    background: none !important;
    color: black !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
