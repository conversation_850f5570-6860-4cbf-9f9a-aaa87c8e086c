<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickShare Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>QuickShare API Test</h1>
    
    <div class="test-section">
        <h2>1. Test Backend Health</h2>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test Single File Upload</h2>
        <input type="file" id="single-file" />
        <button onclick="testSingleUpload()">Upload Single File</button>
        <div id="single-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test Bulk File Upload</h2>
        <input type="file" id="bulk-files" multiple />
        <button onclick="testBulkUpload()">Upload Multiple Files</button>
        <div id="bulk-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Test Generated Links</h2>
        <input type="text" id="test-link" placeholder="Paste generated link here" style="width: 100%; margin: 10px 0;" />
        <button onclick="testLink()">Test Link</button>
        <div id="link-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        async function testHealth() {
            const result = document.getElementById('health-result');
            try {
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                result.textContent = `✅ Health Check: ${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                result.textContent = `❌ Health Check Failed: ${error.message}`;
            }
        }
        
        async function testSingleUpload() {
            const fileInput = document.getElementById('single-file');
            const result = document.getElementById('single-result');
            
            if (!fileInput.files[0]) {
                result.textContent = '❌ Please select a file first';
                return;
            }
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            try {
                result.textContent = '⏳ Uploading...';
                const response = await fetch(`${API_BASE}/api/upload/single`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                result.textContent = `✅ Upload Response: ${JSON.stringify(data, null, 2)}`;
                
                if (data.success && data.file.previewLink) {
                    result.textContent += `\n\n🔗 Preview Link: ${data.file.previewLink}`;
                    result.textContent += `\n🔗 Download Link: ${data.file.downloadLink}`;
                }
            } catch (error) {
                result.textContent = `❌ Upload Failed: ${error.message}`;
            }
        }
        
        async function testBulkUpload() {
            const fileInput = document.getElementById('bulk-files');
            const result = document.getElementById('bulk-result');
            
            if (!fileInput.files.length) {
                result.textContent = '❌ Please select files first';
                return;
            }
            
            const formData = new FormData();
            for (let file of fileInput.files) {
                formData.append('files', file);
            }
            
            try {
                result.textContent = '⏳ Uploading...';
                const response = await fetch(`${API_BASE}/api/upload/bulk`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                result.textContent = `✅ Bulk Upload Response: ${JSON.stringify(data, null, 2)}`;
                
                if (data.success && data.batchDownloadLink) {
                    result.textContent += `\n\n🔗 Batch Link: ${data.batchDownloadLink}`;
                    result.textContent += `\n🔗 Selective Link: ${data.selectiveDownloadLink}`;
                }
            } catch (error) {
                result.textContent = `❌ Bulk Upload Failed: ${error.message}`;
            }
        }
        
        async function testLink() {
            const linkInput = document.getElementById('test-link');
            const result = document.getElementById('link-result');
            
            if (!linkInput.value) {
                result.textContent = '❌ Please enter a link to test';
                return;
            }
            
            try {
                result.textContent = '⏳ Testing link...';
                const response = await fetch(linkInput.value);
                
                if (response.ok) {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        result.textContent = `✅ Link Response (JSON): ${JSON.stringify(data, null, 2)}`;
                    } else {
                        const text = await response.text();
                        result.textContent = `✅ Link Response (HTML): ${text.substring(0, 500)}...`;
                    }
                } else {
                    result.textContent = `❌ Link Failed: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                result.textContent = `❌ Link Test Failed: ${error.message}`;
            }
        }
        
        // Auto-test health on page load
        window.onload = () => {
            testHealth();
        };
    </script>
</body>
</html>
