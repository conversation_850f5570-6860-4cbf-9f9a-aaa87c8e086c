import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { motion } from "framer-motion";

const Navbar = ({ theme, onThemeToggle }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const getThemeIcon = () => {
    switch (theme) {
      case "light":
        return "☀️";
      case "dark":
        return "🌙";
      default:
        return "🌓";
    }
  };

  const isActive = (path) => location.pathname === path;

  return (
    <nav className="navbar">
      <div className="navbar-container">
        {/* Logo */}
        <Link to="/" className="navbar-logo">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="logo-content"
          >
            <span className="logo-icon">📁</span>
            <span className="logo-text">QuickShare</span>
          </motion.div>
        </Link>

        {/* Desktop Navigation */}
        <div className="navbar-menu">
          <Link
            to="/"
            className={`navbar-link ${isActive("/") ? "active" : ""}`}
          >
            🏠 Home
          </Link>

          <Link
            to="/upload"
            className={`navbar-link ${isActive("/upload") ? "active" : ""}`}
          >
            📄 Upload
          </Link>

          <Link
            to="/bulk-upload"
            className={`navbar-link ${
              isActive("/bulk-upload") ? "active" : ""
            }`}
          >
            📦 Bulk Upload
          </Link>
        </div>

        {/* Right Side */}
        <div className="navbar-actions">
          {/* Theme Toggle */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onThemeToggle}
            className="theme-toggle"
            title={`Current theme: ${theme}`}
          >
            {getThemeIcon()}
          </motion.button>

          {/* Mobile Menu Toggle */}
          <button
            className="mobile-menu-toggle"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          className="mobile-menu"
        >
          <Link
            to="/"
            className="mobile-link"
            onClick={() => setIsMenuOpen(false)}
          >
            🏠 Home
          </Link>
          <Link
            to="/upload"
            className="mobile-link"
            onClick={() => setIsMenuOpen(false)}
          >
            📄 Upload
          </Link>
          <Link
            to="/bulk-upload"
            className="mobile-link"
            onClick={() => setIsMenuOpen(false)}
          >
            📦 Bulk Upload
          </Link>
        </motion.div>
      )}
    </nav>
  );
};

export default Navbar;
