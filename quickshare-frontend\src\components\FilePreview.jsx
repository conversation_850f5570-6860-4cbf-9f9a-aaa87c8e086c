import { useState, useEffect } from "react";
import { motion } from "framer-motion";

const FilePreview = ({ file, onRemove, className = "" }) => {
  const [preview, setPreview] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!file) return;

    const generatePreview = async () => {
      setIsLoading(true);
      
      try {
        if (file.type.startsWith("image/")) {
          // Image preview
          const reader = new FileReader();
          reader.onload = (e) => {
            setPreview({
              type: "image",
              url: e.target.result,
            });
            setIsLoading(false);
          };
          reader.readAsDataURL(file);
        } else if (file.type.startsWith("video/")) {
          // Video preview
          const reader = new FileReader();
          reader.onload = (e) => {
            setPreview({
              type: "video",
              url: e.target.result,
            });
            setIsLoading(false);
          };
          reader.readAsDataURL(file);
        } else if (file.type === "application/pdf") {
          // PDF preview (just show icon for now)
          setPreview({
            type: "pdf",
            icon: "📄",
          });
          setIsLoading(false);
        } else if (file.type.startsWith("audio/")) {
          // Audio preview
          setPreview({
            type: "audio",
            icon: "🎵",
          });
          setIsLoading(false);
        } else if (file.type.includes("zip") || file.type.includes("rar")) {
          // Archive preview
          setPreview({
            type: "archive",
            icon: "📦",
          });
          setIsLoading(false);
        } else {
          // Generic file
          setPreview({
            type: "file",
            icon: "📁",
          });
          setIsLoading(false);
        }
      } catch (error) {
        console.error("Error generating preview:", error);
        setPreview({
          type: "error",
          icon: "❌",
        });
        setIsLoading(false);
      }
    };

    generatePreview();
  }, [file]);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (!file) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className={`file-preview ${className}`}
    >
      <div className="preview-container">
        {isLoading ? (
          <div className="preview-loading">
            <div className="loading-spinner"></div>
          </div>
        ) : (
          <div className="preview-content">
            {preview?.type === "image" && (
              <img
                src={preview.url}
                alt={file.name}
                className="preview-image"
              />
            )}
            {preview?.type === "video" && (
              <video
                src={preview.url}
                className="preview-video"
                controls
                muted
              />
            )}
            {(preview?.type === "pdf" ||
              preview?.type === "audio" ||
              preview?.type === "archive" ||
              preview?.type === "file" ||
              preview?.type === "error") && (
              <div className="preview-icon">
                <span className="file-icon">{preview.icon}</span>
              </div>
            )}
          </div>
        )}
        
        {onRemove && (
          <button
            onClick={() => onRemove(file)}
            className="remove-button"
            title="Remove file"
          >
            ✕
          </button>
        )}
      </div>
      
      <div className="file-info">
        <div className="file-name" title={file.name}>
          {file.name}
        </div>
        <div className="file-details">
          <span className="file-size">{formatFileSize(file.size)}</span>
          <span className="file-type">{file.type || "Unknown"}</span>
        </div>
      </div>
    </motion.div>
  );
};

export default FilePreview;
