import { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Toaster } from "react-hot-toast";
import { motion, AnimatePresence } from "framer-motion";

// Components
import Navbar from "./components/Navbar";
import Home from "./pages/Home";
import SingleUpload from "./pages/SingleUpload";
import BulkUpload from "./pages/BulkUpload";
import BatchDownload from "./pages/BatchDownload";
import SelectiveDownload from "./pages/SelectiveDownload";
import Download from "./pages/Download";

// Styles
import "./App.css";

// Main App Component
function AppContent() {
  const [theme, setTheme] = useState("auto");

  // Theme management
  useEffect(() => {
    const savedTheme = localStorage.getItem("quickshare-theme") || "auto";
    setTheme(savedTheme);
    applyTheme(savedTheme);
  }, []);

  const applyTheme = (selectedTheme) => {
    const root = document.documentElement;

    if (selectedTheme === "auto") {
      const prefersDark = window.matchMedia(
        "(prefers-color-scheme: dark)"
      ).matches;
      root.setAttribute("data-theme", prefersDark ? "dark" : "light");
    } else {
      root.setAttribute("data-theme", selectedTheme);
    }
  };

  const toggleTheme = () => {
    const themes = ["light", "dark", "auto"];
    const currentIndex = themes.indexOf(theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];

    setTheme(nextTheme);
    localStorage.setItem("quickshare-theme", nextTheme);
    applyTheme(nextTheme);
  };

  return (
    <div className="app">
      <Navbar theme={theme} onThemeToggle={toggleTheme} />

      <AnimatePresence mode="wait">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/upload" element={<SingleUpload />} />
          <Route path="/bulk-upload" element={<BulkUpload />} />
          <Route path="/batch/:batchId" element={<BatchDownload />} />
          <Route path="/select/:batchId" element={<SelectiveDownload />} />
          <Route path="/download/:id" element={<Download />} />
          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
      </AnimatePresence>

      <footer className="app-footer">
        <div className="footer-content">
          <div className="footer-section">
            <h3>📁 QuickShare</h3>
            <p>The most advanced file sharing platform</p>
          </div>
          <div className="footer-section">
            <h4>Features</h4>
            <ul>
              <li>Bulk Upload</li>
              <li>QR Code Sharing</li>
              <li>Auto-Generated ZIP</li>
              <li>Selective Download</li>
              <li>Cross-Device Access</li>
            </ul>
          </div>
          <div className="footer-section">
            <h4>Industry-Leading Limits</h4>
            <ul>
              <li>🚀 1GB per file (Guests)</li>
              <li>🔥 2GB per file (Registered)</li>
              <li>⚡ 10GB per file (Premium)</li>
              <li>📦 Up to 20 files per batch</li>
              <li>♾️ Unlimited downloads</li>
              <li>📱 QR codes for all files</li>
            </ul>
          </div>
          <div className="footer-section">
            <h4>Security</h4>
            <ul>
              <li>End-to-End Encryption</li>
              <li>Virus Scanning</li>
              <li>Auto-Expiration</li>
              <li>Password Protection</li>
            </ul>
          </div>
        </div>
        <div className="footer-bottom">
          <p>Built with ❤️ using MERN Stack | © 2024 QuickShare</p>
        </div>
      </footer>

      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: "var(--card-bg)",
            color: "var(--text-primary)",
            border: "1px solid var(--border-color)",
          },
        }}
      />
    </div>
  );
}

// Main App with Router
function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;
