import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import axios from "axios";
import toast from "react-hot-toast";
import { API_ENDPOINTS } from "../config/api";

const SelectiveDownload = () => {
  const { batchId } = useParams();
  const [batchData, setBatchData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedFiles, setSelectedFiles] = useState(new Set());
  const [creatingZip, setCreatingZip] = useState(false);
  const [zipDownloadLink, setZipDownloadLink] = useState(null);

  useEffect(() => {
    fetchBatchData();
  }, [batchId]);

  const fetchBatchData = async () => {
    try {
      const response = await axios.get(API_ENDPOINTS.BATCH_INFO(batchId));
      if (response.data.success) {
        setBatchData(response.data.batch);
      } else {
        setError("Batch not found");
      }
    } catch (error) {
      console.error("Error fetching batch data:", error);
      setError("Failed to load batch data");
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (mimetype) => {
    if (mimetype.startsWith("image/")) return "🖼️";
    if (mimetype.startsWith("video/")) return "🎥";
    if (mimetype.startsWith("audio/")) return "🎵";
    if (mimetype.includes("pdf")) return "📄";
    if (mimetype.includes("zip") || mimetype.includes("rar")) return "📦";
    return "📁";
  };

  const toggleFileSelection = (fileId) => {
    const newSelected = new Set(selectedFiles);
    if (newSelected.has(fileId)) {
      newSelected.delete(fileId);
    } else {
      newSelected.add(fileId);
    }
    setSelectedFiles(newSelected);
  };

  const selectAll = () => {
    if (selectedFiles.size === batchData.files.length) {
      setSelectedFiles(new Set());
    } else {
      setSelectedFiles(new Set(batchData.files.map((f) => f.id)));
    }
  };

  const getSelectedSize = () => {
    return batchData.files
      .filter((file) => selectedFiles.has(file.id))
      .reduce((sum, file) => sum + file.size, 0);
  };

  const createSelectiveZip = async () => {
    if (selectedFiles.size === 0) {
      toast.error("Please select at least one file");
      return;
    }

    setCreatingZip(true);
    try {
      const response = await axios.post(`/api/batch/${batchId}/selective-zip`, {
        selectedFileIds: Array.from(selectedFiles),
      });

      if (response.data.success) {
        setZipDownloadLink(response.data.zipDownloadLink);
        toast.success(`ZIP created with ${selectedFiles.size} files!`);
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error("ZIP creation error:", error);
      toast.error(error.response?.data?.message || "Failed to create ZIP");
    } finally {
      setCreatingZip(false);
    }
  };

  const downloadSelected = () => {
    if (selectedFiles.size === 0) {
      toast.error("Please select at least one file");
      return;
    }

    // Download each selected file individually
    batchData.files
      .filter((file) => selectedFiles.has(file.id))
      .forEach((file, index) => {
        setTimeout(() => {
          window.open(file.downloadLink, "_blank");
        }, index * 500); // Stagger downloads to avoid browser blocking
      });

    toast.success(`Downloading ${selectedFiles.size} files...`);
  };

  if (loading) {
    return (
      <div className="page selective-download-page">
        <div className="container">
          <div className="loading-state">
            <div className="spinner-large"></div>
            <p>Loading files...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !batchData) {
    return (
      <div className="page selective-download-page">
        <div className="container">
          <div className="error-state">
            <div className="error-icon">❌</div>
            <h2>Batch Not Found</h2>
            <p>
              The requested batch of files could not be found or may have
              expired.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      style={{
        minHeight: "100vh",
        background:
          "linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #faf5ff 100%)",
        paddingTop: "80px",
      }}
    >
      <div className="container-center" style={{ padding: "2rem 1rem" }}>
        <div style={{ maxWidth: "1200px", margin: "0 auto" }}>
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            style={{ textAlign: "center", marginBottom: "3rem" }}
          >
            <h1
              style={{
                fontSize: "3rem",
                fontWeight: "900",
                marginBottom: "1rem",
                color: "#1e293b",
                lineHeight: "1.2",
              }}
            >
              🎯 Selective Download
            </h1>
            <p
              style={{
                fontSize: "1.25rem",
                color: "#64748b",
                maxWidth: "600px",
                margin: "0 auto 2rem",
                lineHeight: "1.6",
              }}
            >
              Choose exactly which files you want to download from this batch
            </p>
            {batchData.description && (
              <div
                style={{
                  marginTop: "1.5rem",
                  padding: "1rem",
                  background: "#f0f9ff",
                  borderRadius: "0.75rem",
                  border: "1px solid #bfdbfe",
                  maxWidth: "600px",
                  margin: "1.5rem auto 0",
                }}
              >
                <p style={{ color: "#374151", margin: 0 }}>
                  <span style={{ fontWeight: "600" }}>Description:</span>{" "}
                  {batchData.description}
                </p>
              </div>
            )}
          </motion.div>

          {/* Selection Controls */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="card-professional p-6 mb-8 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4"
          >
            <div className="flex items-center space-x-4">
              <div className="text-lg font-semibold text-gray-900 dark:text-white">
                {selectedFiles.size} of {batchData.files.length} files selected
              </div>
              {selectedFiles.size > 0 && (
                <div className="text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">
                  {formatFileSize(getSelectedSize())}
                </div>
              )}
            </div>

            <div className="flex flex-wrap gap-3">
              <button onClick={selectAll} className="btn-outline-pro">
                {selectedFiles.size === batchData.files.length
                  ? "Deselect All"
                  : "Select All"}
              </button>

              <button
                onClick={downloadSelected}
                disabled={selectedFiles.size === 0}
                className={`btn-primary-pro ${
                  selectedFiles.size === 0
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              >
                ⬇️ Download Selected ({selectedFiles.size})
              </button>

              <button
                onClick={createSelectiveZip}
                disabled={selectedFiles.size === 0 || creatingZip}
                className={`btn-secondary-pro ${
                  selectedFiles.size === 0 || creatingZip
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              >
                {creatingZip ? (
                  <>
                    <div className="spinner-professional"></div>
                    Creating ZIP...
                  </>
                ) : (
                  `📦 Create ZIP (${selectedFiles.size})`
                )}
              </button>
            </div>
          </motion.div>

          {/* ZIP Download Link */}
          <AnimatePresence>
            {zipDownloadLink && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="zip-download-section"
              >
                <div className="zip-success">
                  <h3>📦 ZIP File Ready!</h3>
                  <p>Your selected files have been packaged into a ZIP file</p>
                  <a
                    href={zipDownloadLink}
                    className="btn btn-primary btn-large"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    📥 Download ZIP File
                  </a>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Files Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-12">
            {batchData.files.map((file, index) => (
              <motion.div
                key={file.id}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.6 + index * 0.05 }}
                className={`file-card-professional cursor-pointer transition-all duration-300 ${
                  selectedFiles.has(file.id)
                    ? "file-card-selected ring-2 ring-green-500 bg-green-50 dark:bg-green-900 dark:bg-opacity-20"
                    : "hover:border-blue-500 hover:shadow-glow"
                }`}
                onClick={() => toggleFileSelection(file.id)}
              >
                <div className="flex items-center space-x-4">
                  <input
                    type="checkbox"
                    checked={selectedFiles.has(file.id)}
                    onChange={() => toggleFileSelection(file.id)}
                    onClick={(e) => e.stopPropagation()}
                    className="w-5 h-5 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 dark:focus:ring-green-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />

                  <div className="text-4xl">{getFileIcon(file.mimetype)}</div>

                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                      {file.originalName}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {formatFileSize(file.size)}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500 font-mono">
                      {file.mimetype}
                    </p>
                    {file.downloadCount > 0 && (
                      <p className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                        Downloaded {file.downloadCount} times
                      </p>
                    )}
                  </div>

                  <div className="flex flex-col space-y-2">
                    <a
                      href={file.downloadLink}
                      className="btn-professional bg-blue-600 text-white hover:bg-blue-700 text-sm px-4 py-2"
                      target="_blank"
                      rel="noopener noreferrer"
                      onClick={(e) => e.stopPropagation()}
                    >
                      ⬇️ Download
                    </a>
                    {file.qrCode && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          const newWindow = window.open();
                          newWindow.document.write(`
                            <html>
                              <head><title>QR Code - ${
                                file.originalName
                              }</title></head>
                              <body style="text-align: center; padding: 20px; font-family: Arial, sans-serif; background: #f9fafb;">
                                <div style="max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 25px rgba(0,0,0,0.1);">
                                  <h2 style="color: #1f2937; margin-bottom: 20px;">${
                                    file.originalName
                                  }</h2>
                                  <img src="${
                                    file.qrCode
                                  }" alt="QR Code" style="max-width: 250px; border: 2px solid #e5e7eb; padding: 15px; border-radius: 10px;">
                                  <p style="color: #6b7280; margin: 20px 0 10px;">Scan this QR code with your mobile device to download the file</p>
                                  <p style="color: #9ca3af; font-size: 14px; margin: 0;">Size: ${formatFileSize(
                                    file.size
                                  )}</p>
                                </div>
                              </body>
                            </html>
                          `);
                        }}
                        className="btn-professional border-2 border-gray-300 text-gray-700 hover:border-blue-500 hover:text-blue-600 text-sm px-4 py-2"
                      >
                        📱 QR
                      </button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Footer Info */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 1.0 }}
            className="selective-footer"
          >
            <div className="footer-stats">
              <div className="stat-item">
                <span className="stat-number">{batchData.totalFiles}</span>
                <span className="stat-label">Total Files</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">
                  {formatFileSize(batchData.totalSize)}
                </span>
                <span className="stat-label">Total Size</span>
              </div>
              <div className="stat-item">
                <span className="stat-number">
                  {new Date(batchData.uploadDate).toLocaleDateString()}
                </span>
                <span className="stat-label">Uploaded</span>
              </div>
            </div>

            <div className="saas-features">
              <h3>🚀 QuickShare Pro Features</h3>
              <ul>
                <li>✅ Selective file downloads with checkboxes</li>
                <li>✅ Custom ZIP creation from selected files</li>
                <li>✅ QR codes for mobile sharing</li>
                <li>✅ Download analytics and tracking</li>
                <li>✅ Batch management and organization</li>
              </ul>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default SelectiveDownload;
