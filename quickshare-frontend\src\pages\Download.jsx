import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { motion } from "framer-motion";
import axios from "axios";
import toast from "react-hot-toast";
import { API_ENDPOINTS } from "../config/api";

const Download = () => {
  const { id } = useParams();
  const [fileData, setFileData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchFileData();
  }, [id]);

  const fetchFileData = async () => {
    try {
      setLoading(true);
      // Get file info first
      const response = await axios.get(API_ENDPOINTS.DOWNLOAD_INFO(id));

      if (response.data.success) {
        setFileData(response.data.file);
      } else {
        setError(response.data.message || "File not found");
      }
    } catch (error) {
      console.error("Error fetching file data:", error);
      setError(error.response?.data?.message || "Failed to load file data");
    } finally {
      setLoading(false);
    }
  };

  const downloadFile = () => {
    setDownloading(true);
    try {
      // Open download link in new tab
      window.open(fileData.downloadLink, "_blank");
      toast.success("Download started!");
    } catch (error) {
      console.error("Download error:", error);
      toast.error("Download failed");
    } finally {
      setDownloading(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (mimetype) => {
    if (mimetype.startsWith("image/")) return "🖼️";
    if (mimetype.startsWith("video/")) return "🎥";
    if (mimetype.startsWith("audio/")) return "🎵";
    if (mimetype.includes("pdf")) return "📄";
    if (mimetype.includes("word") || mimetype.includes("document")) return "📝";
    if (mimetype.includes("excel") || mimetype.includes("spreadsheet"))
      return "📊";
    if (mimetype.includes("powerpoint") || mimetype.includes("presentation"))
      return "📈";
    if (
      mimetype.includes("zip") ||
      mimetype.includes("rar") ||
      mimetype.includes("archive")
    )
      return "📦";
    return "📁";
  };

  if (loading) {
    return (
      <div className="page download-page">
        <div className="container">
          <div className="loading-container">
            <div className="spinner-large"></div>
            <h2>Loading file information...</h2>
            <p>Please wait while we fetch your file</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page download-page">
        <div className="container">
          <div className="error-container">
            <div className="error-icon">❌</div>
            <h2>File Not Found</h2>
            <p>{error}</p>
            <a href="/" className="btn btn-primary">
              Go Home
            </a>
          </div>
        </div>
      </div>
    );
  }

  if (!fileData) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="page download-page"
    >
      <div className="container">
        <div className="download-container">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className="download-header"
          >
            <h1>📁 File Download</h1>
            <p>Preview and download your file</p>
          </motion.div>

          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="file-preview-card large"
          >
            <div className="file-preview-header">
              <div className="file-icon large">
                {getFileIcon(fileData.mimetype)}
              </div>
              <div className="file-details">
                <h2>{fileData.originalName}</h2>
                <div className="file-meta">
                  <span>Size: {formatFileSize(fileData.size)}</span>
                  <span>Type: {fileData.mimetype}</span>
                  <span>Downloads: {fileData.downloadCount}</span>
                  <span>
                    Uploaded:{" "}
                    {new Date(fileData.uploadDate).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            <div className="download-actions">
              <button
                onClick={downloadFile}
                disabled={downloading}
                className="btn btn-primary btn-large"
              >
                {downloading ? (
                  <>
                    <div className="spinner"></div>
                    Preparing Download...
                  </>
                ) : (
                  "📥 Download File"
                )}
              </button>
              <button
                onClick={() => {
                  if (navigator.share) {
                    navigator.share({
                      title: fileData.originalName,
                      url: window.location.href,
                    });
                  } else {
                    navigator.clipboard.writeText(window.location.href);
                    toast.success("Link copied to clipboard!");
                  }
                }}
                className="btn btn-outline"
              >
                📤 Share Link
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default Download;
