import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { motion } from "framer-motion";
import axios from "axios";
import toast from "react-hot-toast";
import { API_ENDPOINTS } from "../config/api";

const Download = () => {
  const { id } = useParams();
  const [fileData, setFileData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [downloading, setDownloading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (id) {
      fetchFileData();
    }
  }, [id]);

  const fetchFileData = async () => {
    try {
      setLoading(true);
      console.log("Fetching file data for ID:", id);
      console.log("API endpoint:", API_ENDPOINTS.DOWNLOAD_INFO(id));

      // Get file info first
      const response = await axios.get(API_ENDPOINTS.DOWNLOAD_INFO(id));
      console.log("File response:", response.data);

      if (response.data.success) {
        setFileData(response.data.file);
      } else {
        setError(response.data.message || "File not found");
      }
    } catch (error) {
      console.error("Error fetching file data:", error);
      console.error("Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        url: error.config?.url,
      });
      setError(error.response?.data?.message || "Failed to load file data");
    } finally {
      setLoading(false);
    }
  };

  const downloadFile = () => {
    setDownloading(true);
    try {
      // Open download link in new tab
      window.open(fileData.downloadLink, "_blank");
      toast.success("Download started!");
    } catch (error) {
      console.error("Download error:", error);
      toast.error("Download failed");
    } finally {
      setDownloading(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (mimetype) => {
    if (mimetype.startsWith("image/")) return "🖼️";
    if (mimetype.startsWith("video/")) return "🎥";
    if (mimetype.startsWith("audio/")) return "🎵";
    if (mimetype.includes("pdf")) return "📄";
    if (mimetype.includes("word") || mimetype.includes("document")) return "📝";
    if (mimetype.includes("excel") || mimetype.includes("spreadsheet"))
      return "📊";
    if (mimetype.includes("powerpoint") || mimetype.includes("presentation"))
      return "📈";
    if (
      mimetype.includes("zip") ||
      mimetype.includes("rar") ||
      mimetype.includes("archive")
    )
      return "📦";
    return "📁";
  };

  if (loading) {
    return (
      <div className="page download-page">
        <div className="container">
          <div className="loading-container">
            <div className="spinner-large"></div>
            <h2>Loading file information...</h2>
            <p>Please wait while we fetch your file</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page download-page">
        <div className="container">
          <div className="error-container">
            <div className="error-icon">❌</div>
            <h2>File Not Found</h2>
            <p>{error}</p>
            <a href="/" className="btn btn-primary">
              Go Home
            </a>
          </div>
        </div>
      </div>
    );
  }

  if (!fileData) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="page batch-download-page"
    >
      <div className="container">
        <div className="batch-header">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <h1>📁 File Download</h1>
            <p>Preview and download your file</p>
            {fileData.description && (
              <div className="batch-description">
                <strong>Description:</strong> {fileData.description}
              </div>
            )}
          </motion.div>
        </div>

        <div className="batch-info">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="batch-stats"
          >
            <div className="stat-item">
              <span className="stat-number">1</span>
              <span className="stat-label">File</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">
                {formatFileSize(fileData.size)}
              </span>
              <span className="stat-label">Size</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">
                {new Date(fileData.uploadDate).toLocaleDateString()}
              </span>
              <span className="stat-label">Uploaded</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{fileData.downloadCount}</span>
              <span className="stat-label">Downloads</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="download-all-section"
          >
            <button
              onClick={downloadFile}
              disabled={downloading}
              className="btn btn-primary btn-large"
            >
              {downloading ? (
                <>
                  <div className="spinner"></div>
                  Preparing Download...
                </>
              ) : (
                "📥 Download File"
              )}
            </button>
            <p>Download this file to your device</p>
          </motion.div>
        </div>

        <div className="files-section">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            <h2>File Details</h2>
            <div className="files-grid">
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9 }}
                className="file-download-card"
              >
                <div className="file-icon">
                  {getFileIcon(fileData.mimetype)}
                </div>
                <div className="file-info">
                  <h3>{fileData.originalName}</h3>
                  <p>{formatFileSize(fileData.size)}</p>
                  <span className="file-type">{fileData.mimetype}</span>
                </div>
                <div className="file-actions">
                  <a
                    href={fileData.downloadLink}
                    className="btn btn-primary"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    ⬇️ Download
                  </a>
                  {fileData.qrCode && (
                    <button
                      onClick={() => {
                        const newWindow = window.open();
                        newWindow.document.write(`
                          <html>
                            <head><title>QR Code - ${
                              fileData.originalName
                            }</title></head>
                            <body style="text-align: center; padding: 20px; font-family: Arial, sans-serif;">
                              <h2>${fileData.originalName}</h2>
                              <img src="${
                                fileData.qrCode
                              }" alt="QR Code" style="max-width: 300px; border: 1px solid #ddd; padding: 20px;">
                              <p>Scan this QR code with your mobile device to download the file</p>
                              <p style="color: #666; font-size: 14px;">Size: ${formatFileSize(
                                fileData.size
                              )}</p>
                            </body>
                          </html>
                        `);
                      }}
                      className="btn btn-outline"
                    >
                      📱 QR Code
                    </button>
                  )}
                  <button
                    onClick={() => {
                      if (navigator.share) {
                        navigator.share({
                          title: fileData.originalName,
                          url: window.location.href,
                        });
                      } else {
                        navigator.clipboard.writeText(window.location.href);
                        toast.success("Link copied to clipboard!");
                      }
                    }}
                    className="btn btn-outline"
                  >
                    📤 Share Link
                  </button>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default Download;
