import { useState } from "react";
import { motion } from "framer-motion";
import { useDropzone } from "react-dropzone";
import axios from "axios";
import toast from "react-hot-toast";

const BulkUpload = () => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [uploadResult, setUploadResult] = useState(null);
  const [options, setOptions] = useState({
    createZip: true,
    expiresIn: "",
    description: "",
  });

  // Helper function to format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const maxFileSize = 100 * 1024 * 1024; // 100MB for guests
  const maxFiles = 20;

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: (acceptedFiles, rejectedFiles) => {
      if (rejectedFiles.length > 0) {
        toast.error(
          `Some files were rejected. Max size: ${formatFileSize(maxFileSize)}`
        );
      }

      if (acceptedFiles.length + files.length > maxFiles) {
        toast.error(`Maximum ${maxFiles} files allowed`);
        return;
      }

      const newFiles = acceptedFiles.map((file) => ({
        file,
        id: Math.random().toString(36).substr(2, 9),
        progress: 0,
        status: "pending", // pending, uploading, completed, error
      }));

      setFiles((prev) => [...prev, ...newFiles]);
    },
    maxSize: maxFileSize,
    maxFiles: maxFiles,
  });

  const removeFile = (fileId) => {
    setFiles(files.filter((f) => f.id !== fileId));
  };

  const handleUpload = async () => {
    if (files.length === 0) {
      toast.error("Please select files to upload");
      return;
    }

    setUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();

      files.forEach(({ file }) => {
        formData.append("files", file);
      });

      // Add options
      if (options.createZip) formData.append("createZip", "true");
      if (options.expiresIn) formData.append("expiresIn", options.expiresIn);
      if (options.description)
        formData.append("description", options.description);

      const response = await axios.post(
        "http://localhost:5000/api/upload/bulk",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );

            // Update progress for all files
            setFiles((prev) =>
              prev.map((f) => ({
                ...f,
                progress: percentCompleted,
                status: percentCompleted === 100 ? "completed" : "uploading",
              }))
            );
          },
        }
      );

      if (response.data.success) {
        setUploadResult(response.data);
        toast.success(
          `Successfully uploaded ${response.data.files.length} files!`
        );
      } else {
        throw new Error(response.data.message || "Upload failed");
      }
    } catch (error) {
      console.error("Bulk upload error:", error);
      toast.error(error.response?.data?.message || "Upload failed");

      setFiles((prev) =>
        prev.map((f) => ({
          ...f,
          status: "error",
        }))
      );
    } finally {
      setUploading(false);
    }
  };

  const resetUpload = () => {
    setFiles([]);
    setUploadResult(null);
    setUploadProgress({});
  };

  const getFileIcon = (file) => {
    const type = file.type;
    if (type.startsWith("image/")) return "🖼️";
    if (type.startsWith("video/")) return "🎥";
    if (type.startsWith("audio/")) return "🎵";
    if (type.includes("pdf")) return "📄";
    if (type.includes("zip") || type.includes("rar")) return "📦";
    return "📁";
  };

  if (uploadResult) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="page bulk-upload-page"
      >
        <div className="container">
          <div className="upload-success">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="success-content"
            >
              <div className="success-icon">🎉</div>
              <h1>Bulk Upload Successful!</h1>
              <p>Successfully uploaded {uploadResult.files.length} files</p>

              <div className="upload-summary">
                <div className="summary-stat">
                  <span className="stat-number">
                    {uploadResult.files.length}
                  </span>
                  <span className="stat-label">Files Uploaded</span>
                </div>
                <div className="summary-stat">
                  <span className="stat-number">
                    {formatFileSize(uploadResult.summary.totalSize)}
                  </span>
                  <span className="stat-label">Total Size</span>
                </div>
                {uploadResult.zipDownloadLink && (
                  <div className="summary-stat">
                    <span className="stat-number">📦</span>
                    <span className="stat-label">ZIP Created</span>
                  </div>
                )}
              </div>

              <div className="share-link-section">
                <h3>📤 Share this link:</h3>
                <div className="link-container">
                  <input
                    type="text"
                    value={uploadResult.batchDownloadLink}
                    readOnly
                    className="link-input"
                  />
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(
                        uploadResult.batchDownloadLink
                      );
                      toast.success("Link copied to clipboard!");
                    }}
                    className="copy-button"
                  >
                    📋 Copy Link
                  </button>
                </div>
                <p className="link-description">
                  Anyone with this link can view and download all{" "}
                  {uploadResult.files.length} files
                </p>

                <div className="test-actions">
                  <button
                    onClick={() => {
                      setUploadResult(null);
                      setFiles([]);
                    }}
                    className="btn btn-primary"
                  >
                    📁 Upload More Files
                  </button>
                </div>
              </div>

              <div className="upload-summary">
                <h3>✅ Upload Complete!</h3>
                <div className="summary-stats">
                  <div className="stat">
                    <span className="stat-number">
                      {uploadResult.files.length}
                    </span>
                    <span className="stat-label">Files Uploaded</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">
                      {formatFileSize(
                        uploadResult.files.reduce(
                          (sum, file) => sum + file.size,
                          0
                        )
                      )}
                    </span>
                    <span className="stat-label">Total Size</span>
                  </div>
                </div>
                <p className="success-message">
                  🎉 Your files have been uploaded successfully! Share the link
                  above with anyone you want to give access to these files.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="page bulk-upload-page"
    >
      <div className="container">
        <div className="bulk-upload-header">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <h1>📦 Bulk Upload</h1>
            <p>
              Upload up to {maxFiles} files at once and get automatic ZIP
              downloads
            </p>
          </motion.div>
        </div>

        <div className="bulk-upload-container">
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="upload-zone"
          >
            <div
              {...getRootProps()}
              className={`dropzone ${isDragActive ? "active" : ""} ${
                files.length > 0 ? "has-files" : ""
              }`}
            >
              <input {...getInputProps()} />
              <div className="dropzone-content">
                <div className="dropzone-icon">📦</div>
                <h3>
                  {isDragActive
                    ? "Drop files here..."
                    : "Drag & drop files here, or click to select"}
                </h3>
                <p>
                  Maximum {maxFiles} files • {formatFileSize(maxFileSize)} per
                  file • Guest User
                </p>
              </div>
            </div>

            {files.length > 0 && (
              <div className="files-list">
                <div className="files-header">
                  <h3>
                    Selected Files ({files.length}/{maxFiles})
                  </h3>
                  <button
                    onClick={() => setFiles([])}
                    className="btn btn-small btn-outline"
                  >
                    Clear All
                  </button>
                </div>

                <div className="files-grid">
                  {files.map((fileItem) => (
                    <div key={fileItem.id} className="file-item">
                      <div className="file-icon">
                        {getFileIcon(fileItem.file)}
                      </div>
                      <div className="file-info">
                        <h4>{fileItem.file.name}</h4>
                        <p>{formatFileSize(fileItem.file.size)}</p>
                        {fileItem.status === "uploading" && (
                          <div className="file-progress">
                            <div className="progress-bar">
                              <div
                                className="progress-fill"
                                style={{ width: `${fileItem.progress}%` }}
                              ></div>
                            </div>
                            <span>{fileItem.progress}%</span>
                          </div>
                        )}
                        {fileItem.status === "completed" && (
                          <div className="file-status success">✅ Uploaded</div>
                        )}
                        {fileItem.status === "error" && (
                          <div className="file-status error">❌ Failed</div>
                        )}
                      </div>
                      {!uploading && (
                        <button
                          onClick={() => removeFile(fileItem.id)}
                          className="remove-file"
                        >
                          ✕
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {files.length > 0 && !uploading && (
              <div className="upload-options">
                <h3>Upload Options</h3>
                <div className="options-grid">
                  <label className="option-item">
                    <input
                      type="checkbox"
                      checked={options.createZip}
                      onChange={(e) =>
                        setOptions({ ...options, createZip: e.target.checked })
                      }
                    />
                    <span>📦 Create ZIP file for bulk download</span>
                  </label>

                  <div className="option-item">
                    <label>⏰ Auto-delete after:</label>
                    <select
                      value={options.expiresIn}
                      onChange={(e) =>
                        setOptions({ ...options, expiresIn: e.target.value })
                      }
                      className="form-input"
                    >
                      <option value="">Never</option>
                      <option value="1">1 day</option>
                      <option value="7">7 days</option>
                      <option value="30">30 days</option>
                    </select>
                  </div>

                  <div className="option-item">
                    <label>📝 Description (optional):</label>
                    <input
                      type="text"
                      value={options.description}
                      onChange={(e) =>
                        setOptions({ ...options, description: e.target.value })
                      }
                      className="form-input"
                      placeholder="Describe this batch of files..."
                    />
                  </div>
                </div>

                <button
                  onClick={handleUpload}
                  disabled={uploading}
                  className="btn btn-primary btn-large"
                >
                  {uploading ? (
                    <>
                      <div className="spinner"></div>
                      Uploading {files.length} files...
                    </>
                  ) : (
                    `📤 Upload ${files.length} Files`
                  )}
                </button>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default BulkUpload;
